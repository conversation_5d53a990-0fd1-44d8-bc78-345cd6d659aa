﻿using System;
using System.ComponentModel.DataAnnotations;
using Trading.API.Data.DTO;

namespace Trading.API.Data.DTO;

/// <summary>
/// Search DTO for AUCA auction imports
/// </summary>
public class AucaSearchDTO : BaseSearchDTO
{
  public AucaSearchFilters Filters { get; set; } = new AucaSearchFilters() { };
}

/// <summary>
/// Search filters for AUCA auction imports
/// </summary>
public class AucaSearchFilters : BaseFilterGuid
{
  public Guid? InventUserId { get; set; }

  /// <summary>
  /// General search term that can match against VRM, make, model, etc.
  /// </summary>
  public string SearchTerm { get; set; }

  /// <summary>
  /// Start date for filtering by import/sale date
  /// </summary>
  public DateTime? DateFrom { get; set; }

  /// <summary>
  /// End date for filtering by import/sale date
  /// </summary>
  public DateTime? DateTo { get; set; }

  public int? Status { get; set; }
}

public class AucaAuctionSearchDTO: BaseSearchDTO
{
  public AucaAuctionSearchFilters Filters { get; set; } = new AucaAuctionSearchFilters() { };
}

public class AucaAuctionSearchFilters : BaseFilter
{
  public string SearchTerm { get; set; }
  public string AuctionType { get; set; }
  public string AuctionLocation { get; set; }
  public DateTime? DateFrom { get; set; }
  public DateTime? DateTo { get; set; }
  public int? ActivityStatus { get; set; }
}