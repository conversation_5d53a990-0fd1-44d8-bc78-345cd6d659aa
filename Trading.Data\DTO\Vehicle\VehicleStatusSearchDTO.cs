﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO
{
  public class VehicleStatusSearchDTO
  {
    public string VRM { get; set; }
    public Guid? CustomerId { get; set; }
    public uint? PerPage { get; set; } = 20;
    public uint? Page { get; set; } = 1;

    public bool? Relistable { get; set; } = false;
    public bool? NoMOT { get; set; } = false;
  }
}
