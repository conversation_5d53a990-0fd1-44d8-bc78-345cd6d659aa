﻿﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using Trading.API.Data.DTO.AutoTrader;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Trading.API.Data.Models.AutoTrader;
using Trading.API.Data.Models.InspectCollect.VehicleData;
using Trading.Services.InspectCollect.Classes.VehicleLookup;
using Trading.Services.InspectCollect.Interfaces;
using Trading.Services.Interfaces.AutoTrader;

namespace Trading.Services.InspectCollect.Tests.Helpers
{
    public static class MockHelpers
    {
        public static Mock<ICVehicleLookupInterface> CreateMockLookupService()
        {
            var mock = new Mock<ICVehicleLookupInterface>();
            
            // Setup default successful responses
            mock.Setup(x => x.GetICVehicleLookupData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(ICVehicleLookupResult.CreateSuccess(CreateSampleLookupData()));
                
            mock.Setup(x => x.GetICVehicleValuationData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(ICCapDataResult.CreateSuccess(CreateSampleCapData()));
                
            return mock;
        }

        public static Mock<IAutoTraderService> CreateMockAutoTraderService()
        {
            var mock = new Mock<IAutoTraderService>();
            
            mock.Setup(x => x.GetVehicleValuationsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleValuationResponse());
                
            mock.Setup(x => x.GetVehicleFeaturesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleFeaturesResponse());
                
            mock.Setup(x => x.GetVehicleMetricsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleMetricsResponse());
                
            mock.Setup(x => x.GetCombinedVehicleDataAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleCombinedResponse());
                
            mock.Setup(x => x.GetVehicleValuationWithFeaturesAsync(
                It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<int>(), It.IsAny<List<string>>(), 
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleValuationResponse());
                
            return mock;
        }

        public static Mock<IAutoTraderClient> CreateMockAutoTraderClient()
        {
            var mock = new Mock<IAutoTraderClient>();
            
            mock.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(new AuthTokenResponse { AccessToken = "test-token", ExpiresIn = 3600 });
                
            return mock;
        }

        public static Mock<ICVehicleInterface> CreateMockVehicleService()
        {
            var mock = new Mock<ICVehicleInterface>();
            
            mock.Setup(x => x.GetOrCreateICVehicleData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<ICVehicleLookupDataDTO>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleVehicleDTO());
                
            mock.Setup(x => x.GetExistingICVehicleData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleVehicleDTO());
                
            mock.Setup(x => x.GetExistingICVehicleData(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleVehicleDTO());
                
            mock.Setup(x => x.CreateICCapData(It.IsAny<Guid>(), It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<ICVehicleCheckResponseDTO>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleCapData());
                
            return mock;
        }

        public static Mock<ICResponseDataInterface> CreateMockResponseService()
        {
            var mock = new Mock<ICResponseDataInterface>();
            
            mock.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(CreateSampleResponseDTO());
                
            return mock;
        }

        public static Mock<ILogger<ICVehicleDataService>> CreateMockLogger()
        {
            return new Mock<ILogger<ICVehicleDataService>>();
        }

        public static Mock<IMapper> CreateMockMapper()
        {
            var mock = new Mock<IMapper>();
            
            // Setup basic mapping behaviors
            mock.Setup(x => x.Map<ICCapDataDTO>(It.IsAny<ICCapData>()))
                .Returns(CreateSampleCapDataDTO());
                
            mock.Setup(x => x.Map<AutoTraderValuationDTO>(It.IsAny<ATVehicleValuations>()))
                .Returns(CreateSampleAutoTraderValuationDTO());
                
            return mock;
        }

        // Sample data creation methods
        public static ICVehicleLookupDataDTO CreateSampleLookupData()
        {
            return new ICVehicleLookupDataDTO
            {
                VRM = "TEST123",
                VIN = "TESTVIN123456789",
                MakeName = "Test Make",
                ModelName = "Test Model",
                DerivName = "Test Derivative",
                ICVehicleId = Guid.NewGuid()
            };
        }

        public static ICVehicleCheckResponseDTO CreateSampleCapData()
        {
            return new ICVehicleCheckResponseDTO
            {
                VRM = "TEST123",
                MakeName = "Test Make",
                ModelName = "Test Model",
                DerivName = "Test Derivative"
            };
        }

        public static ICVehicleDTO CreateSampleVehicleDTO()
        {
            return new ICVehicleDTO
            {
                Id = Guid.NewGuid(),
                VRM = "TEST123",
                VIN = "TESTVIN123456789",
                MakeName = "Test Make",
                ModelName = "Test Model",
                DerivName = "Test Derivative",
                CreatedByResponseId = Guid.NewGuid()
            };
        }

        public static ICResponseDTO CreateSampleResponseDTO()
        {
            return new ICResponseDTO
            {
                Id = Guid.NewGuid(),
                ICVehicleId = Guid.NewGuid(),
                ICVehicle = CreateSampleVehicleDTO()
            };
        }

        public static ATVehicleValuationsResponse CreateSampleValuationResponse()
        {
            return new ATVehicleValuationsResponse
            {
                Valuations = new ATVehicleValuations
                {
                    TradeAverage = 15000,
                    TradeGood = 16000,
                    TradeClean = 17000,
                    RetailAverage = 18000,
                    RetailGood = 19000,
                    RetailClean = 20000
                }
            };
        }

        public static ATVehicleFeaturesResponse CreateSampleFeaturesResponse()
        {
            return new ATVehicleFeaturesResponse
            {
                Features = new List<ATVehicleFeature>
                {
                    new ATVehicleFeature { Name = "Air Conditioning", Category = "Comfort" },
                    new ATVehicleFeature { Name = "Alloy Wheels", Category = "Exterior" }
                }
            };
        }

        public static ATVehicleMetricsResponse CreateSampleMetricsResponse()
        {
            return new ATVehicleMetricsResponse
            {
                Metrics = new ATVehicleMetrics
                {
                    AverageOdometerReading = 50000,
                    AveragePrice = 15000
                }
            };
        }

        public static ATCombinedVehicleResponse CreateSampleCombinedResponse()
        {
            return new ATCombinedVehicleResponse
            {
                LatestFeatureList = new AutoTraderFeatureList(),
                LatestVehicleMetrics = new AutoTraderVehicleMetricData(),
                LatestValuation = new AutoTraderValuation(),
                LatestVehicle = new AutoTraderVehicle()
            };
        }

        public static ICCapDataDTO CreateSampleCapDataDTO()
        {
            return new ICCapDataDTO
            {
                ICVehicleId = Guid.NewGuid()
            };
        }

        public static AutoTraderValuationDTO CreateSampleAutoTraderValuationDTO()
        {
            return new AutoTraderValuationDTO
            {
                TradeAverage = 15000,
                TradeGood = 16000,
                TradeClean = 17000,
                RetailAverage = 18000,
                RetailGood = 19000,
                RetailClean = 20000
            };
        }

        public static List<AutoTraderFeatureDTO> CreateSampleAutoTraderFeaturesList()
        {
            return new List<AutoTraderFeatureDTO>
            {
                new AutoTraderFeatureDTO { Name = "Air Conditioning", Category = "Comfort", ResponseSelected = true },
                new AutoTraderFeatureDTO { Name = "Alloy Wheels", Category = "Exterior", ResponseSelected = true },
                new AutoTraderFeatureDTO { Name = "Bluetooth", Category = "Technology", ResponseSelected = false }
            };
        }
    }
}
