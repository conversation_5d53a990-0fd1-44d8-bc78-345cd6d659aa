using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Vehicle;
using Trading.API.Data.Models;
using Trading.API.Helpers;
using Trading.Services.Extensions;
using Trading.Services.ExternalDTO;
using Trading.Services.Interfaces;

namespace Trading.API.Controllers
{
  [Route("api/vehicle")]
  [ApiController]
  public class VehicleController : ControllerBase
  {
    private readonly IVehicleService _vehicleService;
    private readonly IMapper _mapper;
    private readonly IVehicleCheckService _vehicleCheckService;

    public VehicleController(IVehicleService vehicleService, IMapper mapper, IVehicleCheckService vehicleCheckService)
    {
      _vehicleService = vehicleService;
      _mapper = mapper;
      _vehicleCheckService = vehicleCheckService;
    }

    [HttpGet]
    [Route("{id}")]
    [Authorize]
    public async Task<ActionResult<VehicleListDTO>> GetVehicle(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        var vehicle = await _vehicleService.GetVehicle(id, cancellationToken);
        return Ok(vehicle);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

   

    [HttpGet]
    [Route("vehicleStatusCheck")]
    public async Task<ActionResult<VehicleListDTO>> VehicleStatusCheck([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        VehicleStatusSearchDTO searchDTO = JsonConvert.DeserializeObject<VehicleStatusSearchDTO>(query);
        var vehicles = await _vehicleService.VehicleStatusCheck(searchDTO, cancellationToken);

        return Ok(vehicles);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("")]
    [Authorize]
    public async Task<IActionResult> Search([FromQuery] string query, CancellationToken cancellationToken)
    {
      VehicleSearchDTO dto = new VehicleSearchDTO() { };

      if (query != null)
      {
        dto =  JsonConvert.DeserializeObject<VehicleSearchDTO>(query);
      }

      try
      {
        var vehicles = await _vehicleService.Search(dto, cancellationToken);

        return Ok(vehicles);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/customer/{customerId}/vehicles")]
    [Authorize]
    public async Task<IActionResult> GetCustomerVehicles(Guid id, CancellationToken cancellationToken)
    {
      if (User.ContactId() != null)
      {

      }

      try
      {
        var vehicles = await _vehicleService.GetAllByCustomer(id, cancellationToken);
        return Ok(vehicles);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    /*
    [HttpGet]
    [Route("getActiveVehicles")]
    [Authorize]
    public async Task<ActionResult<VehicleListDTO>> GetActiveVehicles([FromQuery] VehicleListDTO vehicleListDTO, CancellationToken cancellationToken)
    {

      try
      {
        var active = await _vehicleService.GetActiveVehicles(vehicleListDTO, cancellationToken);
        return Ok(active);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }/
    }
    */

    [HttpDelete("{vehicleId}")]
    public async Task<IActionResult> DeleteVehicle(Guid vehicleId, CancellationToken cancellationToken)
    {
      try
      {
        await _vehicleService.DeleteVehicle(this.User.CustomerId().Value, vehicleId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> updateVehicle(Guid id, [FromBody]VehicleDTO dto, CancellationToken cancellationToken)
    {
      dto.Id = id;
      try
      {
        var vehicleDTO = await _vehicleService.UpdateVehicle(dto, cancellationToken);
        return Ok(vehicleDTO);
      }
      catch { }

      return NoContent();
    }

    [HttpPost("")] // Add vehicle
    public async Task<IActionResult> addVehicle(VehicleDTO dto, CancellationToken cancellationToken)
    {
      try
      {
        if (dto.Id != null)
        {
          return BadRequest(new ApplicationException("VehicleId should be 0 for new vehicles"));
        }

        var vehicleDTO = await _vehicleService.AddVehicle(dto, cancellationToken);

        return Ok(vehicleDTO);
      }
      catch { }

      return NoContent();
    }

    [HttpPost("createVehicleFromLookup")]
    public async Task<IActionResult> createVehicleFromLookup([FromBody] CreateVehicleDTO dto, CancellationToken cancellationToken)
    {
      if (dto.CustomerId == this.User.CustomerId() || this.User.IsAdmin())
      {
        if (dto.ContactId == null)
        {
          dto.ContactId = User.ContactId();
        }

        try
        {
          var vehicleDTO = await _vehicleService.CreateVehicle(dto, cancellationToken);
          return Ok(vehicleDTO);
        }
        catch { }
      }

      return NoContent();
    }

    [HttpPatch]
    [Route("{vehicleId}")]
    public async Task<IActionResult> patchVehicle(Guid vehicleId, [FromBody] JsonPatchDocument<Vehicle> patch, CancellationToken cancellationToken)
    {

      try
      {
        var customerId = User.CustomerId();
        var vehicle = await _vehicleService.Patch(vehicleId, customerId.Value, patch, cancellationToken);
        return Ok(vehicle);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("refreshDVLAData")] // WHAT DOES THIS DO ?
    public async Task<IActionResult> updateAllDVLAData(CancellationToken cancellationToken)
    {
      try
      {
        await _vehicleService.UpdateAllDVLAData(cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [ResponseCache(Duration = 600)]
    [Route("/api/dvla/{vrm}")]
    public async Task<IActionResult> getDVLAData(string vrm, CancellationToken cancellationToken)
    {
      try
      {
        var data = await _vehicleService.GetDVLAData(vrm, cancellationToken);
        return Ok(data);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost]
    [Route("{vehicleId}/serviceHistory")]
    public async Task<IActionResult> AddServiceHistory(Guid vehicleId, [FromBody]ServiceHistoryDTO serviceHistoryDTO, CancellationToken cancellationToken)
    {
      try
      {
        var newServiceHistory = await _vehicleService.AddServiceHistory(vehicleId, serviceHistoryDTO, cancellationToken);
        return Ok(newServiceHistory);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPatch]
    [Route("{vehicleId}/serviceHistory/{serviceHistoryId}")]
    public async Task<IActionResult> PatchServiceHistory(Guid vehicleId, Guid serviceHistoryId, [FromBody]JsonPatchDocument<ServiceHistory> patch, CancellationToken cancellationToken)
    {
      try
      {
        var id = await _vehicleService.PatchServiceHistory(vehicleId, serviceHistoryId, patch, cancellationToken);
        return Ok(id);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{vehicleId}/serviceHistory/{serviceHistoryId}")]
    public async Task<IActionResult> DeleteServiceHistory(Guid vehicleId, Guid serviceHistoryId, CancellationToken cancellationToken)
    {
      try
      {
        var ok = await _vehicleService.DeleteServiceHistory(vehicleId, serviceHistoryId, cancellationToken);
        return Ok(ok);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    //[ResponseCache(Duration = 600)]
    [Route("{vehicleId}/audit")]
    public async Task<IActionResult> GetVehicleHistory(Guid vehicleId, [FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var searchDTO = JsonConvert.DeserializeObject<VehicleSearchDTO>(query);
        searchDTO.Filters.VehicleId = vehicleId;
        var data = await _vehicleService.Search(searchDTO, cancellationToken);
        return Ok(data);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpGet]
    [ResponseCache(Duration = 6)]
    [Route("lookup")]
    public async Task<IActionResult> GetMatchingVehicles([FromQuery] string query, CancellationToken cancellationToken)
    {
      try
      {
        var dto = JsonConvert.DeserializeObject<VehicleSearchDTO>(query);

        dto.Limit = 10;

        var response = await _vehicleService.GetMatchingVehicles(dto, cancellationToken);
        var data = _mapper.Map<IEnumerable<Vehicle>, IEnumerable<VehicleBasicDTO>>(response);

        return Ok(data);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/vehicle/{id}/update-mot")]
    public async Task<IActionResult> UpdateMOT(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        await _vehicleService.UpdateMOTHistory(id, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/vehicle/{id}/update-provenance")]
    public async Task<IActionResult> UpdateProvenance(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        VRMLookupDataDTO dto = new VRMLookupDataDTO { vehicleId = id };
        var res = await _vehicleCheckService.UpdateVehicleChecks(dto, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpPost("/api/vehicle/{id}/upsert-provenance")]
    public async Task<IActionResult> UpsertProvenanceCheck(Guid id, [FromBody] VehicleProvenanceUpdateDTO dto)
    {
      dto.VehicleId = id;

      if (!ModelState.IsValid)
      {
        return BadRequest(ModelState);
      }

      try
      {
        var vehicleCheck = await _vehicleCheckService.UpsertProvenanceCheck(dto);
        return Ok(vehicleCheck);
      }
      catch (Exception ex)
      {
        // Log the exception
        return StatusCode(500, "An error occurred while processing your request.");
      }
    }


    [HttpGet]
    [Route("/api/vehicle/{id}/provenance")]
    public async Task<IActionResult> GetProvenance(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _vehicleCheckService.GetVehicleCheck(id, Data.Enums.VehicleCheckTypeEnum.Provenance, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }



    [HttpGet]
    [Route("/api/listing/{advertId}/vehicle-provenance")]
    [OutputCache(Duration = 600, PolicyName = "ConditionalCacheWithAuthPolicy")] // incicate policy name
    public async Task<IActionResult> GetProvenanceByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _vehicleService.GetVehicleProvenanceByAdvert(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [OutputCache(Duration = 600, PolicyName = "ConditionalCacheWithAuthPolicy")] // incicate policy name
    [Route("/api/listing/{advertId}/vehicle-valuation")]
    public async Task<IActionResult> GetValuationByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _vehicleService.GetVehicleValuationByAdvert(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/listing/{advertId}/vehicle-mot-history")]
    public async Task<IActionResult> GetMOTHistoryByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _vehicleService.GetMOTHistoryByAdvert(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [OutputCache(Duration = 600, PolicyName = "ConditionalCacheWithAuthPolicy")] // incicate policy name
    [Route("/api/listing/{advertId}/vehicle-media")]
    public async Task<IActionResult> GetVehicleMediaByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _vehicleService.GetVehicleMediaByAdvert(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/listing/{advertId}/vehicle-service-history")]
    public async Task<IActionResult> GetVehicleServiceHistoryByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _vehicleService.GetVehicleServiceHistoryByAdvert(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [OutputCache(Duration = 600, PolicyName = "ConditionalCacheWithAuthPolicy")] // indicate policy name
    [Route("/api/listing/{advertId}/vehicle-tyre-info")]
    public async Task<IActionResult> GetVehicleTyreInfoByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _vehicleService.GetVehicleTyreInfoByAdvert(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [OutputCache(Duration = 600, PolicyName = "ConditionalCacheWithAuthPolicy")] // incicate policy name
    [Route("/api/listing/{advertId}/vehicle-options")]
    public async Task<IActionResult> GetVehicleOptionsByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _vehicleService.GetVehicleOptionsByAdvert(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/listing/{advertId}/contact-seller-data")]
    [OutputCache(Duration = 600, PolicyName = "ConditionalCacheWithAuthPolicy")] // incicate policy name
    public async Task<IActionResult> GetContactSellerByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _vehicleService.GetContactSellerDataByAdvert(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/vehicle/{id}/valuation")]
    public async Task<IActionResult> GetValuation(Guid id, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _vehicleCheckService.GetVehicleCheck(id, Data.Enums.VehicleCheckTypeEnum.Valuation, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
