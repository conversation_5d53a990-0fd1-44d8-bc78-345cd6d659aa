﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.Valuation;

namespace Trading.API.Data.DTO.Valuation
{
  public class ValuationAttributeDTO
  {
    public string Name { get; set; } // user friendly name
    public ValuationLookupTypeEnum LookupType { get; set; } 
    public bool IsPillar { get; set; } // does this lookup represent a pillar (false if a vluation node)
    public IEnumerable<SelectItemDTO> LookupItems { get; set; }
    public int Order { get; set; }
  }
}
