﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect.VehicleLookup;

public class ICVehicleEnquiryDTO
{
  public Guid ResponseId { get; set; }
  public string VRM { get; set; } 
  public string VIN { get; set; }
  public uint? Odometer { get; set; }
  public bool? IgnoreCache { get; set; }
}
