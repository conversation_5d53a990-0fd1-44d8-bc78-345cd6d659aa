﻿﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using Trading.API.Data.DTO.AutoTrader;
using Trading.API.Data.DTO.InspectCollect;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Trading.API.Data.Models.AutoTrader;
using Trading.Services.InspectCollect.Classes.VehicleLookup;
using Trading.Services.InspectCollect.Interfaces;
using Trading.Services.InspectCollect.Tests.Helpers;
using Trading.Services.Interfaces.AutoTrader;

namespace Trading.Services.InspectCollect.Tests
{
    public class ICVehicleDataServiceAdvancedTests : TestBase
    {
        private readonly Mock<ICVehicleLookupInterface> _mockLookupService;
        private readonly Mock<IAutoTraderService> _mockAutoTraderService;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ICVehicleInterface> _mockVehicleService;
        private readonly Mock<ILogger<ICVehicleDataService>> _mockLogger;
        private readonly Mock<IAutoTraderClient> _mockAutoTraderClient;
        private readonly Mock<ICResponseDataInterface> _mockResponseService;
        private readonly ICVehicleDataService _service;

        public ICVehicleDataServiceAdvancedTests()
        {
            _mockLookupService = MockHelpers.CreateMockLookupService();
            _mockAutoTraderService = MockHelpers.CreateMockAutoTraderService();
            _mockMapper = MockHelpers.CreateMockMapper();
            _mockVehicleService = MockHelpers.CreateMockVehicleService();
            _mockLogger = MockHelpers.CreateMockLogger();
            _mockAutoTraderClient = MockHelpers.CreateMockAutoTraderClient();
            _mockResponseService = MockHelpers.CreateMockResponseService();

            _service = new ICVehicleDataService(
                _mockLookupService.Object,
                _mockAutoTraderService.Object,
                _mockMapper.Object,
                _mockVehicleService.Object,
                _mockLogger.Object,
                _mockAutoTraderClient.Object,
                _mockResponseService.Object
            );
        }

        #region Vehicle Context Tests

        [Fact]
        public async Task GetFeatureValuationInternal_WithCompleteVehicleContext_ReturnsSuccess()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
            var response = MockHelpers.CreateSampleResponseDTO();
            
            // Setup vehicle with complete data for feature valuation
            response.ICVehicle.CapData = new ICCapDataDTO
            {
                LatestICValuation = new ICValuationDTO
                {
                    DerivativeId = "TEST_DERIV_123",
                    FirstRegisteredDate = DateTime.Now.AddYears(-3),
                    Odometer = 50000
                }
            };

            response.ICVehicle.AutoTraderData = new ICAutoTraderDataDTO
            {
                LatestFeatureList = new AutoTraderFeatureListDTO
                {
                    Features = new List<AutoTraderFeatureDTO>
                    {
                        new AutoTraderFeatureDTO { Name = "Air Conditioning", ResponseSelected = true },
                        new AutoTraderFeatureDTO { Name = "Alloy Wheels", ResponseSelected = true }
                    }
                }
            };

            _mockResponseService.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            // Act
            var result = await _service.GetFeatureValuationInternal(enquiry, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task GetFeatureValuationInternal_WithIncompleteVehicleContext_StillProcesses()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
            var response = MockHelpers.CreateSampleResponseDTO();
            
            // Setup vehicle with incomplete data (missing derivative ID)
            response.ICVehicle.CapData = new ICCapDataDTO
            {
                LatestICValuation = new ICValuationDTO
                {
                    DerivativeId = null, // Missing derivative ID
                    FirstRegisteredDate = DateTime.Now.AddYears(-3),
                    Odometer = 50000
                }
            };

            _mockResponseService.Setup(x => x.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            // Act
            var result = await _service.GetFeatureValuationInternal(enquiry, CancellationToken.None);

            // Assert
            // Should still process but may not call feature valuation API
            Assert.True(result.Success);
        }

        #endregion

        #region Data Processing Tests

        [Fact]
        public async Task GetValuationDataInternal_WithOnlyCapDataSucceeding_ReturnsSuccess()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();
            
            // Setup AutoTrader to fail but CAP to succeed
            _mockAutoTraderService.Setup(x => x.GetCombinedVehicleDataAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new HttpRequestException("AutoTrader service unavailable"));

            // Act
            var result = await _service.GetValuationDataInternal(enquiry, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.NotNull(result.Data.ICVehicle);
        }

        [Fact]
        public async Task GetValuationDataInternal_WithAutoTraderAndCapSucceeding_ProcessesBothDataTypes()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateValidEnquiryWithVRM();

            // Act
            var result = await _service.GetValuationDataInternal(enquiry, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.NotNull(result.Data.ICVehicle);

            // Verify both services were called
            _mockLookupService.Verify(x => x.GetICVehicleValuationData(It.IsAny<ICVehicleEnquiryDTO>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockAutoTraderService.Verify(x => x.GetCombinedVehicleDataAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region Authentication Tests

        [Fact]
        public async Task GetAutoTraderValuationAsync_WhenAuthenticationSucceeds_CallsValuationService()
        {
            // Arrange
            var authResponse = new AuthTokenResponse { AccessToken = "valid-token", ExpiresIn = 3600 };
            _mockAutoTraderClient.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(authResponse);

            // Act
            var result = await _service.GetAutoTraderValuationAsync("TEST123", 50000, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            _mockAutoTraderClient.Verify(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
            _mockAutoTraderService.Verify(x => x.GetVehicleValuationsAsync("TEST123", 50000, null, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetAutoTraderFeaturesAsync_WhenAuthenticationSucceeds_CallsFeaturesService()
        {
            // Arrange
            var authResponse = new AuthTokenResponse { AccessToken = "valid-token", ExpiresIn = 3600 };
            _mockAutoTraderClient.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(authResponse);

            // Act
            var result = await _service.GetAutoTraderFeaturesAsync("TEST123", CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            _mockAutoTraderClient.Verify(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
            _mockAutoTraderService.Verify(x => x.GetVehicleFeaturesAsync("TEST123", null, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetAutoTraderMetricsAsync_WhenAuthenticationSucceeds_CallsMetricsService()
        {
            // Arrange
            var authResponse = new AuthTokenResponse { AccessToken = "valid-token", ExpiresIn = 3600 };
            _mockAutoTraderClient.Setup(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(authResponse);

            // Act
            var result = await _service.GetAutoTraderMetricsAsync("TEST123", 50000, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            _mockAutoTraderClient.Verify(x => x.AuthenticateAsync(It.IsAny<CancellationToken>()), Times.Once);
            _mockAutoTraderService.Verify(x => x.GetVehicleMetricsAsync("TEST123", 50000, null, It.IsAny<CancellationToken>()), Times.Once);
        }

        #endregion

        #region Error Handling Tests

        [Fact]
        public async Task GetAutoTraderValuationAsync_WhenServiceThrowsException_ReturnsFailure()
        {
            // Arrange
            _mockAutoTraderService.Setup(x => x.GetVehicleValuationsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new HttpRequestException("Service temporarily unavailable"));

            // Act
            var result = await _service.GetAutoTraderValuationAsync("TEST123", 50000, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("Service temporarily unavailable", result.ErrorMessage);
        }

        [Fact]
        public async Task GetAutoTraderFeaturesAsync_WhenServiceThrowsException_ReturnsFailure()
        {
            // Arrange
            _mockAutoTraderService.Setup(x => x.GetVehicleFeaturesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new TimeoutException("Request timeout"));

            // Act
            var result = await _service.GetAutoTraderFeaturesAsync("TEST123", CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("Request timeout", result.ErrorMessage);
        }

        [Fact]
        public async Task GetAutoTraderMetricsAsync_WhenServiceThrowsException_ReturnsFailure()
        {
            // Arrange
            _mockAutoTraderService.Setup(x => x.GetVehicleMetricsAsync(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("Invalid vehicle data"));

            // Act
            var result = await _service.GetAutoTraderMetricsAsync("TEST123", 50000, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("Invalid vehicle data", result.ErrorMessage);
        }

        #endregion

        #region Logging Tests

        [Fact]
        public async Task GetLookupDataInternal_WhenValidationFails_LogsWarning()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateInvalidEnquiry();

            // Act
            var result = await _service.GetLookupDataInternal(enquiry, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            
            // Verify warning was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("GetLookupDataInternal validation failed")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetCapDataAsync_WhenValidationFails_LogsWarning()
        {
            // Arrange
            var enquiry = TestDataHelpers.CreateInvalidEnquiry();

            // Act
            var result = await _service.GetCapDataAsync(enquiry, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            
            // Verify warning was logged
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Warning,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("GetCapDataAsync validation failed")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        #endregion
    }
}
