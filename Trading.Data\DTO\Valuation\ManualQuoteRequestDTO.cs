﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Valuation
{
  public class ManualQuoteRequestDTO 
  {
    public Guid ExistingQuoteId { get; set; }
    public Guid LeadVehicleId { get; set; }
    public int NewValuation { get; set; }
    public Guid? ContactId { get; set; } 
    public string ContactNote { get; set; }

    public DateTime? Expires { get; set; }
  }
}
