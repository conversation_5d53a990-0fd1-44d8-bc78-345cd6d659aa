﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect.Location;

public class ICLocationDTO : BaseModelEntityDTO
{
  public Guid ICContainerGroupId { get; set; }
  public string SiteName { get; set; }
}

public class CreateICLocationDTO
{
  [Required]
  public Guid ICContainerGroupId { get; set; }

  [Required]
  [MaxLength(255)]
  public string SiteName { get; set; }
}

public class UpdateICLocationDTO
{
  [Required]
  [MaxLength(255)]
  public string SiteName { get; set; }
}

public class ICLocationSearchDTO : BaseSearchDTO
{
  public ICLocationSearchFilters Filters { get; set; } = new ICLocationSearchFilters();
}

public class ICLocationSearchFilters : BaseFilter
{
  public string SearchTerm { get; set; }

  public Guid? ICContainerGroupId { get; set; }
}


// User Location DTOs
public class ICUserLocationDTO : BaseModelEntityDTO
{
  public Guid ICUserId { get; set; }
  public Guid ICLocationId { get; set; }
  public ICUserDTO ICUser { get; set; }
  public ICLocationDTO ICLocation { get; set; }
}

public class CreateICUserLocationDTO
{
  [Required]
  public Guid ICUserId { get; set; }

  [Required]
  public Guid ICLocationId { get; set; }
}

// Default Location DTO
public class SetDefaultLocationDTO
{
  [Required]
  public Guid ICUserId { get; set; }

  [Required]
  public Guid ICLocationId { get; set; }
}