﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.DTO.Imports.AUCA;
using Trading.Services.Extensions;
using Trading.Services.Interfaces.AucaImports;

namespace Trading.API.Remarq.Controllers.AuctionImports;

[ApiController]
[Route("api/auction-import")]
[Authorize(Roles = "ADMIN, POWER_USER, IMPORT_ADMIN")]
public class AuctionImportController : ControllerBase
{
  public class ReservePriceUpdateModel
  {
    public decimal ReservePrice { get; set; }
  }

  private readonly IAuctionImportService _importService;

  public AuctionImportController(IAuctionImportService importService)
  {
    _importService = importService;
  }

  /// <summary>
  /// Search for auction import items based on component type and filters
  /// </summary>
  [HttpPost("search")]
  public async Task<IActionResult> Search([FromBody] AucaSearchDTO searchDTO, CancellationToken cancellationToken)
  {
    var results = await _importService.SearchAsync(searchDTO, cancellationToken);
    return Ok(results);
  }

  [HttpPost("search-auctions")]
  public async Task<IActionResult> SearchAuctions([FromBody] AucaAuctionSearchDTO searchDTO, CancellationToken cancellationToken)
  {
    var results = await _importService.SearchAuctionsAsync(searchDTO, cancellationToken);
    return Ok(results);
  }

  /// <summary>
  /// Mark a vehicle as actioned
  /// </summary>
  [HttpPost("mark-actioned/{id}")]
  public async Task<IActionResult> MarkAsActioned(string id, CancellationToken cancellationToken)
  {
    var result = await _importService.MarkVehicleAsActionedAsync(id, cancellationToken);
    return Ok(new { success = result });
  }

  /// <summary>
  /// Withdraw vehicles
  /// </summary>
  [HttpPost("withdraw")]
  public async Task<IActionResult> WithdrawVehicles([FromBody] ImportVehicleRequest request, CancellationToken cancellationToken)
  {
    var result = await _importService.WithdrawVehiclesAsync(request.VehicleIds, cancellationToken);
    return Ok(new { success = result });
  }

  /// <summary>
  /// Restore a withdrawn vehicle
  /// </summary>
  [HttpPost("restore/{id}")]
  public async Task<IActionResult> RestoreVehicle(string id, CancellationToken cancellationToken)
  {
    var result = await _importService.RestoreVehicleAsync(id, cancellationToken);
    return Ok(new { success = result });
  }

  /// <summary>
  /// Start the import process
  /// </summary>
  [HttpGet("start/{inventUserId}")]
  public async Task<IActionResult> StartImport(Guid inventUserId, CancellationToken cancellationToken)
  {
    var import = await _importService.ImportAuctionsAsync(inventUserId, cancellationToken);
    return Ok(import);
  }

  [HttpGet("invent-users")]
  public async Task<IActionResult> GetInventUsers(CancellationToken cancellationToken)
  {
    var res = await _importService.GetInventUsersAsync(User.IsAdmin(), cancellationToken);
    return Ok(res);
  }

  /// <summary>
  /// Process an import
  /// </summary>
  [HttpPost("process")]
  public async Task<IActionResult> ProcessImport([FromBody] ImportVehicleRequest request, CancellationToken cancellationToken)
  {
    int count = await _importService.ProcessAuctionLotsAsync(request.InventUserId, request.AucaLotIds, cancellationToken);
    return Ok(new { ImportedCount = count });
  }

  [HttpPost("{vehicleId}/update-reserve-price")]
  public async Task<IActionResult> UpdateLotReservePrice(Guid vehicleId, [FromBody] ReservePriceUpdateModel model, CancellationToken cancellationToken)
  {
    var res = await _importService.UpdateLotReservePrice(vehicleId, model.ReservePrice, cancellationToken);
    return Ok(res);
  }

}