﻿﻿using Trading.API.Data.DTO.AutoTrader;
using Trading.API.Data.DTO.InspectCollect.VehicleData;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Trading.Services.InspectCollect.Classes.VehicleLookup;
using Trading.Services.InspectCollect.Tests.Helpers;

namespace Trading.Services.InspectCollect.Tests
{
    public class ICVehicleDataServiceResultTests
    {
        #region ICVehicleLookupResult Tests

        [Fact]
        public void ICVehicleLookupResult_CreateSuccess_ReturnsSuccessResult()
        {
            // Arrange
            var data = MockHelpers.CreateSampleLookupData();

            // Act
            var result = ICVehicleLookupResult.CreateSuccess(data);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(data, result.Data);
            Assert.Null(result.ErrorMessage);
        }

        [Fact]
        public void ICVehicleLookupResult_CreateFailure_ReturnsFailureResult()
        {
            // Arrange
            var errorMessage = "Test error message";

            // Act
            var result = ICVehicleLookupResult.CreateFailure(errorMessage);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
            Assert.Equal(errorMessage, result.ErrorMessage);
        }

        [Fact]
        public void ICVehicleLookupResult_CreateSuccess_WithNullData_ReturnsSuccessResult()
        {
            // Act
            var result = ICVehicleLookupResult.CreateSuccess(null);

            // Assert
            Assert.True(result.Success);
            Assert.Null(result.Data);
            Assert.Null(result.ErrorMessage);
        }

        [Fact]
        public void ICVehicleLookupResult_CreateFailure_WithNullMessage_ReturnsFailureResult()
        {
            // Act
            var result = ICVehicleLookupResult.CreateFailure(null);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
            Assert.Null(result.ErrorMessage);
        }

        #endregion

        #region ICValuationDataResult Tests

        [Fact]
        public void ICValuationDataResult_CreateSuccess_ReturnsSuccessResult()
        {
            // Arrange
            var data = new ICGetValuationDataInternalResponse
            {
                ICVehicle = MockHelpers.CreateSampleVehicleDTO()
            };

            // Act
            var result = ICValuationDataResult.CreateSuccess(data);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(data, result.Data);
            Assert.Null(result.ErrorMessage);
        }

        [Fact]
        public void ICValuationDataResult_CreateFailure_ReturnsFailureResult()
        {
            // Arrange
            var errorMessage = "Valuation service error";

            // Act
            var result = ICValuationDataResult.CreateFailure(errorMessage);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
            Assert.Equal(errorMessage, result.ErrorMessage);
        }

        [Fact]
        public void ICValuationDataResult_CreateSuccess_WithNullData_ReturnsSuccessResult()
        {
            // Act
            var result = ICValuationDataResult.CreateSuccess(null);

            // Assert
            Assert.True(result.Success);
            Assert.Null(result.Data);
            Assert.Null(result.ErrorMessage);
        }

        #endregion

        #region AutoTraderValuationResult Tests

        [Fact]
        public void AutoTraderValuationResult_CreateSuccess_ReturnsSuccessResult()
        {
            // Arrange
            var data = MockHelpers.CreateSampleAutoTraderValuationDTO();

            // Act
            var result = AutoTraderValuationResult.CreateSuccess(data);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(data, result.Data);
            Assert.Null(result.ErrorMessage);
        }

        [Fact]
        public void AutoTraderValuationResult_CreateFailure_ReturnsFailureResult()
        {
            // Arrange
            var errorMessage = "AutoTrader authentication failed";

            // Act
            var result = AutoTraderValuationResult.CreateFailure(errorMessage);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
            Assert.Equal(errorMessage, result.ErrorMessage);
        }

        #endregion

        #region AutoTraderFeaturesResult Tests

        [Fact]
        public void AutoTraderFeaturesResult_CreateSuccess_ReturnsSuccessResult()
        {
            // Arrange
            var data = new List<AutoTraderFeatureDTO>
            {
                new AutoTraderFeatureDTO { Name = "Air Conditioning", Category = "Comfort" }
            };

            // Act
            var result = AutoTraderFeaturesResult.CreateSuccess(data);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(data, result.Data);
            Assert.Null(result.ErrorMessage);
        }

        [Fact]
        public void AutoTraderFeaturesResult_CreateFailure_ReturnsFailureResult()
        {
            // Arrange
            var errorMessage = "Features service unavailable";

            // Act
            var result = AutoTraderFeaturesResult.CreateFailure(errorMessage);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
            Assert.Equal(errorMessage, result.ErrorMessage);
        }

        #endregion

        #region AutoTraderMetricsResult Tests

        [Fact]
        public void AutoTraderMetricsResult_CreateSuccess_ReturnsSuccessResult()
        {
            // Arrange
            var data = new AutoTraderVehicleMetricDataDTO
            {
                AverageOdometerReading = 50000,
                AveragePrice = 15000
            };

            // Act
            var result = AutoTraderMetricsResult.CreateSuccess(data);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(data, result.Data);
            Assert.Null(result.ErrorMessage);
        }

        [Fact]
        public void AutoTraderMetricsResult_CreateFailure_ReturnsFailureResult()
        {
            // Arrange
            var errorMessage = "Metrics service error";

            // Act
            var result = AutoTraderMetricsResult.CreateFailure(errorMessage);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
            Assert.Equal(errorMessage, result.ErrorMessage);
        }

        #endregion

        #region ICCapDataResult Tests

        [Fact]
        public void ICCapDataResult_CreateSuccess_ReturnsSuccessResult()
        {
            // Arrange
            var data = MockHelpers.CreateSampleCapData();

            // Act
            var result = ICCapDataResult.CreateSuccess(data);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(data, result.Data);
            Assert.Null(result.ErrorMessage);
        }

        [Fact]
        public void ICCapDataResult_CreateFailure_ReturnsFailureResult()
        {
            // Arrange
            var errorMessage = "CAP HPI service error";

            // Act
            var result = ICCapDataResult.CreateFailure(errorMessage);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
            Assert.Equal(errorMessage, result.ErrorMessage);
        }

        #endregion

        #region Edge Cases

        [Fact]
        public void AllResults_CreateFailure_WithEmptyString_ReturnsFailureResult()
        {
            // Act & Assert
            var lookupResult = ICVehicleLookupResult.CreateFailure("");
            Assert.False(lookupResult.Success);
            Assert.Equal("", lookupResult.ErrorMessage);

            var valuationResult = ICValuationDataResult.CreateFailure("");
            Assert.False(valuationResult.Success);
            Assert.Equal("", valuationResult.ErrorMessage);

            var autoTraderResult = AutoTraderValuationResult.CreateFailure("");
            Assert.False(autoTraderResult.Success);
            Assert.Equal("", autoTraderResult.ErrorMessage);
        }

        [Fact]
        public void AllResults_CreateFailure_WithWhitespace_ReturnsFailureResult()
        {
            // Arrange
            var whitespaceMessage = "   ";

            // Act & Assert
            var lookupResult = ICVehicleLookupResult.CreateFailure(whitespaceMessage);
            Assert.False(lookupResult.Success);
            Assert.Equal(whitespaceMessage, lookupResult.ErrorMessage);

            var valuationResult = ICValuationDataResult.CreateFailure(whitespaceMessage);
            Assert.False(valuationResult.Success);
            Assert.Equal(whitespaceMessage, valuationResult.ErrorMessage);

            var autoTraderResult = AutoTraderValuationResult.CreateFailure(whitespaceMessage);
            Assert.False(autoTraderResult.Success);
            Assert.Equal(whitespaceMessage, autoTraderResult.ErrorMessage);
        }

        #endregion
    }
}
