﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.Vehicle
{
  public class VehicleProvenanceUpdateDTO
  {
    public Guid? VehicleId { get; set; }

    public uint? Odometer { get; set; }

    // flag checks
    public bool? Scrapped { get; set; }
    public bool? Stolen { get; set; }
    public bool? Finance { get; set; }
  }
}
