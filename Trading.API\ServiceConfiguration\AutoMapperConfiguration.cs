﻿using Microsoft.Extensions.DependencyInjection;
using Trading.Services.DTOMappingProfiles;
using Trading.Services.InspectCollect.Mapping;
using Trading.Services.LeadCRM.Mapping;
using Trading.Services.SiteScan.Mapping;
using Trading.Services.UInspections.Mapping;
using Trading.Services.Valuations.Mapping;

namespace Trading.API.Remarq.ServiceConfiguration
{

  public static partial class ServiceConfigurationExtensions
  {
    public static void RegisterMappingProfiles(this IServiceCollection services)
    {
      services.AddAutoMapper(cfg =>
      {
        cfg.AllowNullCollections = true;
        cfg.AllowNullDestinationValues = true;
      }, typeof(MappingProfile));


      services.AddAutoMapper(cfg =>
      {
        cfg.AllowNullCollections = true;
        cfg.AllowNullDestinationValues = true;
      }, typeof(LeadCRMMappingProfile));

      services.AddAutoMapper(cfg =>
      {
        cfg.AllowNullCollections = true;
        cfg.AllowNullDestinationValues = true;
      }, typeof(UInspectMappingProfile));

      services.AddAutoMapper(cfg =>
      {
        cfg.AllowNullCollections = true;
        cfg.AllowNullDestinationValues = true;
      }, typeof(SiteScanMappingProfile));

      services.AddAutoMapper(cfg =>
      {
        cfg.AllowNullCollections = true;
        cfg.AllowNullDestinationValues = true;
      }, typeof(ValuationMappingProfile));


      services.AddAutoMapper(cfg =>
      {
        cfg.AllowNullCollections = true;
        cfg.AllowNullDestinationValues = true;
      }, typeof(ICMapProfiles));
    }
  }

}
