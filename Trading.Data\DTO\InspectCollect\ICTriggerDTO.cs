﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICTriggerDTO : BaseModelEntityDTO
  {
    public string Name { get; set; }
    public ICTriggerTypeEnum TriggerType { get; set; }
    public bool Wait { get; set; }
    public bool RemoteCall { get; set; }
    public string RemoteTriggerName { get; set; }
    public string PayloadTemplate { get; set; }
  }

  public class ICTriggerSearchDTO : BaseSearchDTO
  {
    public ICTriggerSearchFilters Filters { get; set; } = new ICTriggerSearchFilters();
  }

  public class ICTriggerSearchFilters
  {
    public Guid? Id { get; set; }
    public Guid? ICContainerGroupId { get; set; }
    public string Name { get; set; }
  }

  public class ICTriggerCreateDTO
  {
    public Guid ICContainerGroupId { get; set; }
    public string Name { get; set; }
  }

  public class ICTriggerRequestDTO
  {
    public ICTriggerTypeEnum? ICTriggerType { get; set; }
    public string? TriggerName { get; set; }
    public string? Payload { get; set; }
  }

  public class ICTriggerPayloadVehicleLookup
  {
    public string VRM { get; set; }
    public string VIN { get; set; }
    public int? Odometer { get; set; }
  }
}