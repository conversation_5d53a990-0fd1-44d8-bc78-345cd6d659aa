﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class VehicleDTO_Public : BaseModelEntityDTO
  {
    public Guid? CustomerId { get; set; }
    public string? Vrm { get; set; }
    public string? Colour { get; set; }
    public string? CustomerRef { get; set; }
    public DateTime? MotExpires { get; set; }
    public DateTime? DateOfReg { get; set; }

    public ushort? Doors { get; set; }
    public uint? Odometer { get; set; }
    public ushort? Owners { get; set; }
    public ushort? EngineCc { get; set; }
    public string? Vin { get; set; }
    
    public uint? VehicleTypeId { get; set; }

    public Guid? PrimaryImageId { get; set; }
    public string? PrimaryImageURL { get; set; }
    public uint? Kerbweight { get; set; }
    public AttribvalDTO? VATStatus { get; set; }
    public AttribvalDTO? V5Status { get; set; }
    public bool? Runner { get; set; }
    public short? Grade { get; set; }
    public bool? LogBook { get; set; }
    public bool? ServiceHistory { get; set; }
    public DateTime? LastService { get; set; }
    public sbyte? TyreDepth_NSF { get; set; }
    public sbyte? TyreDepth_NSR { get; set; }
    public sbyte? TyreDepth_OSF { get; set; }
    public sbyte? TyreDepth_OSR { get; set; }
    public sbyte? TyreDepth_Spare { get; set; }
    public uint? Co2 { get; set; }
    public byte? NoOfKeys { get; set; }
    public uint? BHP { get; set; }

    public OdometerUnitEnum? OdometerUnit { get; set; }
    public string? OdometerSuffix { get; set; }
    public uint? ImageCount { get; set; }

    public ServiceHistoryTypeEnum? ServiceHistoryType { get; set; }

    public MakeDTO? Make { get; set; }
    public ModelDTO? Model { get; set; }
    public DerivDTO? Deriv { get; set; }
    public FuelTypeDTO? FuelType { get; set; }
    public BodyTypeDTO? BodyType { get; set; }
    public VehicleColourDTO? VehicleColour { get; set; }
    public TransmissionTypeDTO? TransmissionType { get; set; }
    public PlateDTO? Plate { get; set; }
    public VehicleTypeDTO? VehicleType { get; set; }
    public AddressDTO_Public? Address { get; set; }
    public IEnumerable<AdvertDTO_Public> Adverts { get; set; }
    public IEnumerable<AppraisalDTO> Appraisals { get; set; }
    public IEnumerable<VehicleMediaDTO> VehicleMedia { get; set; }
    public IEnumerable<VehicleMediaDTO> ConditionReports { get; set; }
    public IEnumerable<VehicleAttribDTO> VehicleAttribs { get; set; }
    public IEnumerable<MOTHistoryDTO> MOTHistory { get; set; } = new List<MOTHistoryDTO>();
    public IEnumerable<VehicleMediaDTO> V5Media { get; set; } 
    public IEnumerable<VehicleMediaDTO> ServiceBookMedia { get; set; }
    public IEnumerable<ServiceHistoryDTO> ServiceHistories { get; set; } = new List<ServiceHistoryDTO>();
    public VehicleMediaDTO Walkaround { get; set; }

    public Guid? LatestProvenanceId { get; set; }
    public VehicleCheckDTO LatestProvenance { get; set; }

    public Guid? LatestValuationId { get; set; }
    public VehicleCheckDTO LatestValuation { get; set; }
    public bool HasMOTIssues { get; set; } 

    public bool? ColourChanged { get; set; }
    public string CapId { get; set; }
    public string CapCode { get; set; }
    public string Co2Rating { get; set; }
    public uint? YearOfManufacture { get; set; }
    public Guid? LatestVehicleFaultCheckId { get; set; }
  }
}
