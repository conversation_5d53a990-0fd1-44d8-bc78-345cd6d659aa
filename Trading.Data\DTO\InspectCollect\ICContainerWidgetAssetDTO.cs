﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICContainerWidgetAssetDTO : BaseModelEntityDTO
  {
    public Guid? ICContainerWidgetId { get; set; }
    public ICContainerWidgetDTO? ICContainerWidget { get; set; }
    public Guid? ICAssetId { get; set; }
    public ICAssetDTO? ICAsset { get; set; }
    public Guid? ICStyleId { get; set; }
    public ICStyleDTO? ICStyle { get; set; }
    public uint? Position { get; set; }
  }

  public class ICContainerWidgetAssetSearchDTO : BaseSearchDTO
  {
    public ICContainerWidgetAssetSearchFilters Filters { get; set; } = new ICContainerWidgetAssetSearchFilters();
  }

  public class ICContainerWidgetAssetSearchFilters : BaseFilter
  {
    public string Label { get; set; }
    public Guid? ICContainerWidgetId { get; set; }
  }

  public class ICContainerWidgetAssetCreateDTO
  {
    public Guid? ICContainerWidgetId { get; set; }
  }
}
