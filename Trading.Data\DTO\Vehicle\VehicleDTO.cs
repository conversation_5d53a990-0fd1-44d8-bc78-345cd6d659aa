﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.MechanicalFaults;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO
{
  public class VehicleDTO : VehicleDTO_Public
  {
    public uint? FuelTypeId { get; set; }
    public uint? BodyTypeId { get; set; }
    public uint? TransmissionTypeId { get; set; }
    public uint? VatStatusId { get; set; }
    public uint? V5StatusId { get; set; }
    public uint? TaxStatusId { get; set; }
    public uint? StandInValue { get; set; }

    public Guid? AddressId { get; set; }

    public Guid? ContactId { get; set; }
    public Guid? VehicleMediaId { get; set; }
    public uint? PlateId { get; set; }
    public uint? VehicleColourId { get; set; }

    public uint? MileageRangeId { get; set; }

    public uint? CapacityRangeId { get; set; }

    public int? OdometerUnit { get; set; }
    public uint? MakeId { get; set; }
    public uint? ModelId { get; set; }
    public uint? DerivId { get; set; }
    public string Equipment { get; set; }
    public new AddressDTO Address { get; set; }
    public CustomerDTO Customer { get; set; }
    public ContactDTO Contact { get; set; }
    public AttribvalDTO VATStatus { get; set; }
    public new IEnumerable<AdvertDTO> Adverts { get; set; }
    public new IEnumerable<AppraisalDTO> Appraisals { get; set; }
    public List<VehicleActionHistoryDTO> ActionHistory { get; set; }
    public DVLAVehicleDTO DVLAData { get; set; }

    public VehicleFaultCheckDTO LatestVehicleFaultCheck { get; set; }
  }
}
