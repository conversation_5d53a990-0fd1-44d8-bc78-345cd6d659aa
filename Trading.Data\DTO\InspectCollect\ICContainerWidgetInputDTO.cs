﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Models.InspectCollect;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICContainerWidgetInputDTO : BaseModelEntityDTO
  {
    public Guid ICContainerWidgetId { get; set; }
    public Guid ICInputId { get; set; }
    public ICInputDTO ICInput { get; set; }
    public uint? Position { get; set; }

    public ICAssetDTO ICAsset { get; set; }
    public Guid? ICAssetId { get; set; }
    public bool? HideLabel { get; set; }
    public List<ICContainerWidgetInputStyleDTO> ICContainerWidgetInputStyles { get; set; }
    public List<ICContainerWidgetInputTriggerDTO> ICContainerWidgetInputTriggers { get; set; }
  }


  public class ICContainerWidgetInputSearchDTO : BaseSearchDTO
  {
    public ICContainerWidgetInputSearchFilters Filters { get; set; } = new ICContainerWidgetInputSearchFilters();
  }

  public class ICContainerWidgetInputSearchFilters : BaseFilter
  {
    public Guid? ICContainerWidgetId { get; set; }
  }

  public class ICContainerWidgetInputCreateDTO
  {
    public Guid ICContainerWidgetId { get; set; }
    public Guid? ICInputId { get; set; }
    public uint? Position { get; set; }
  }
}
