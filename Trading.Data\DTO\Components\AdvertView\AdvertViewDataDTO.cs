﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums;

namespace Trading.API.Data.DTO.Components.AdvertView
{
  public class AdvertViewDataDTO
  {
    public Guid Id { get; set; }
    public uint StatusId { get; set; }
    public Guid CustomerId { get; set; }
    public AdvertStatusEnum AdvertStatus {  get; set; }
    public uint SaleTypeId { get; set; }
    public string MakeName { get; set; }
    public string ModelName { get; set; }
    public string DerivName { get; set; }
    public string PlateName { get; set; }
    public string TransmissionTypeName { get; set; }
    public string BodyTypeName { get; set; }
    public int? EngineCc { get; set; }
    public string FuelTypeName { get; set; }
    public uint? Odometer {  get; set; }
    public string Vrm { get; set; }
    public uint GGG { get; set; }
    public uint? CurrentPrice { get; set; }
    public Guid? TopBidGuid { get; set; }
    public Guid? LatestVehicleFaultCheckId { get; set; }
    public bool Runner {  get; set; }
    public string Description { get; set; }

    public string Colour { get; set; }
    public ushort? Doors { get; set; }
    public ushort? Owners { get; set; }

    public ServiceHistoryTypeEnum? ServiceHistoryType { get; set; }
    public string VATStatusName { get; set; }
    public bool LogBook { get; set; }
    public string V5StatusName { get; set; }
  }
}
