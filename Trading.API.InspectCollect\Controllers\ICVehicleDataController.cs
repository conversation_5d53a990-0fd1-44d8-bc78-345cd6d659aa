using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Trading.API.Data.DTO.InspectCollect.VehicleLookup;
using Trading.Services.InspectCollect.Interfaces;

namespace Trading.API.External.Controllers
{
  [Route("api/inspect-collect/vehicle-data")]
  [ApiController]
  public class VehicleDataController : ControllerBase
  {
    private readonly ICVehicleDataInterface _vehicleDataService;

    public VehicleDataController(ICVehicleDataInterface vehicleDataService)
    {
      _vehicleDataService = vehicleDataService;
    }

    /// <summary>
    /// Get AutoTrader valuation data for a vehicle by VRM and odometer reading
    /// </summary>
    /// <param name="vrm">Vehicle Registration Mark</param>
    /// <param name="odometer">Odometer reading in miles</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>AutoTrader valuation response or null if not available</returns>
    [HttpGet("autotrader/valuation/{vrm}/odometer/{odometer}")]
    public async Task<IActionResult> GetAutoTraderValuation(string vrm, int odometer, CancellationToken cancellationToken)
    {
      var validationError = ValidateVrmAndOdometer(vrm, odometer);
      if (validationError != null)
      {
        return BadRequest(validationError);
      }

      var result = await _vehicleDataService.GetAutoTraderValuationAsync(vrm, odometer, cancellationToken);

      if (!result.Success)
      {
        return BadRequest(new { success = false, error = result.ErrorMessage });
      }

      return Ok(new { success = true, data = result.Data });
    }

    /// <summary>
    /// Get AutoTrader metrics data for a vehicle by VRM and odometer reading
    /// </summary>
    /// <param name="vrm">Vehicle Registration Mark</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>AutoTrader metrics response or null if not available</returns>
    [HttpGet("autotrader/metrics/{vrm}/odometer/{odometer}")]
    public async Task<IActionResult> GetAutoTraderMetrics(string vrm, int odometer, CancellationToken cancellationToken)
    {
      var validationError = ValidateVrmAndOdometer(vrm, odometer);
      if (validationError != null)
      {
        return BadRequest(validationError);
      }

      var result = await _vehicleDataService.GetAutoTraderMetricsAsync(vrm, odometer, cancellationToken);

      if (!result.Success)
      {
        return BadRequest(new { success = false, error = result.ErrorMessage });
      }

      return Ok(new { success = true, data = result.Data });
    }

    [HttpGet("autotrader/features/{vrm}")]
    public async Task<IActionResult> GetAutoTraderFeatures(string vrm, CancellationToken cancellationToken)
    {
      if (string.IsNullOrWhiteSpace(vrm))
      {
        return BadRequest(new { success = false, error = "VRM is required" });
      }

      var result = await _vehicleDataService.GetAutoTraderFeaturesAsync(vrm, cancellationToken);

      if (!result.Success)
      {
        return BadRequest(new { success = false, error = result.ErrorMessage });
      }

      return Ok(new { success = true, data = result.Data });
    }

    /// <summary>
    /// Get CAP data for a vehicle by VRM and optional odometer reading
    /// </summary>
    /// <param name="vrm">Vehicle Registration Mark</param>
    /// <param name="odometer">Optional odometer reading in miles</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>CAP data response</returns>
    [HttpGet("cap/{vrm}")]
    public async Task<IActionResult> GetCapDataByVrm(string vrm, [FromQuery] uint? odometer, CancellationToken cancellationToken)
    {
      try
      {
        if (string.IsNullOrWhiteSpace(vrm))
        {
          return BadRequest("VRM is required");
        }

        var enquiry = new ICVehicleEnquiryDTO
        {
          VRM = vrm.ToUpper(),
          Odometer = odometer
        };

        var result = await _vehicleDataService.GetCapDataAsync(enquiry, cancellationToken);
        if (!result.Success)
        {
          return BadRequest(result.ErrorMessage);
        }
        return Ok(result.Data);
      }
      catch (ArgumentException ex)
      {
        return BadRequest(ex.Message);
      }
      catch (Exception ex)
      {
        return StatusCode(500, $"An error occurred while retrieving CAP data: {ex.Message}");
      }
    }

    /// <summary>
    /// Get CAP data for a vehicle by VIN and optional odometer reading
    /// </summary>
    /// <param name="vin">Vehicle Identification Number</param>
    /// <param name="odometer">Optional odometer reading in miles</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>CAP data response</returns>
    [HttpGet("cap/vin/{vin}")]
    public async Task<IActionResult> GetCapDataByVin(string vin, [FromQuery] uint? odometer, CancellationToken cancellationToken)
    {
      try
      {
        if (string.IsNullOrWhiteSpace(vin))
        {
          return BadRequest("VIN is required");
        }

        var enquiry = new ICVehicleEnquiryDTO
        {
          VIN = vin.ToUpper(),
          Odometer = odometer
        };

        var result = await _vehicleDataService.GetCapDataAsync(enquiry, cancellationToken);
        if (!result.Success)
        {
          return BadRequest(result.ErrorMessage);
        }
        return Ok(result.Data);
      }
      catch (ArgumentException ex)
      {
        return BadRequest(ex.Message);
      }
      catch (Exception ex)
      {
        return StatusCode(500, $"An error occurred while retrieving CAP data: {ex.Message}");
      }
    }

    /// <summary>
    /// Validates VRM and odometer parameters
    /// </summary>
    /// <param name="vrm">Vehicle Registration Mark</param>
    /// <param name="odometer">Odometer reading</param>
    /// <returns>Validation error message or null if valid</returns>
    private string? ValidateVrmAndOdometer(string vrm, int odometer)
    {
      if (string.IsNullOrWhiteSpace(vrm))
      {
        return "VRM is required";
      }

      if (odometer < 0)
      {
        return "Odometer reading must be non-negative";
      }

      return null;
    }
  }
}
