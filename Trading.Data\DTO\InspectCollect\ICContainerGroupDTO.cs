﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.Search;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICContainerGroupDTO : BaseModelEntityDTO
  {
    public string Name { get; set; }
    public List<ICContainerDTO> ICContainers { get; set; }
    public List<ICLayoutDTO> ICLayouts { get; set; }
    public ICStyleDTO ICStyle { get; set; }
    public Guid? ICStyleId { get; set; }

  }

  public class ICContainerGroupSearchDTO : BaseSearchDTO
  {
    public ICContainerGroupSearchFilters Filters { get; set; } = new ICContainerGroupSearchFilters();
  }

  public class ICContainerGroupSearchFilters : BaseFilter
  {
    public string? Name { get; set; }
    public bool? IgnoreDeleted { get; set; }
  }

  public class ICContainerGroupCreateDTO
  {
    public string Name { get; set; }
  }
}
