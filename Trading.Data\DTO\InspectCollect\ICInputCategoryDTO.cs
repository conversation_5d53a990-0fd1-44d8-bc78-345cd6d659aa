﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Trading.API.Data.DTO.InspectCollect
{
  public class ICInputCategoryDTO : BaseModelEntityDTO
  {
    public string Label { get; set; }
    public string FieldName { get; set; }
    public int? Sequence { get; set; }
    public Guid? ParentCategoryId { get; set; }
    public ICInputCategoryDTO? ParentCategory { get; set; }
    public List<ICInputDTO> ICInputs { get; set; }

    public Guid? ICContainerGroupId { get; set; }
    public ICContainerGroupDTO ICContainerGroup { get; set; }
  }

  public class ICInputCategorySearchDTO : BaseSearchDTO
  {
    public ICInputCategorySearchFilters Filters { get; set; } = new ICInputCategorySearchFilters();
  }

  public class ICInputCategorySearchFilters : BaseFilter
  {
    public string Label { get; set; }
    public Guid? ICContainerGroupId { get; set; }
  }

  public class ICInputCategoryCreateDTO
  {
    public string Label { get; set; }
    public Guid? ParentCategoryId { get; set; }
    public Guid ICContainerGroupId { get; set; }
  }
}
