﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.Enums.Valuation;

namespace Trading.API.Data.DTO.Valuation
{
  // this is a cut-down version of the ValuationRequestDTO for external customers 
  public class QuoteRequestDTO
  {
    public string VRM { get; set; }

    public uint? Mileage { get; set; }


    public string Name { get; set; }
    public string Email { get; set; }
    public string Mobile { get; set; }
    public string Phone { get; set; }

    public string ExternalAppraisalCode { get; set; }
  }
}
