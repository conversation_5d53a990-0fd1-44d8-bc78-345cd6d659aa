using System;
using Trading.API.Data.Models;

namespace Trading.API.Data.DTO
{
  public class VehicleMediaDTO : BaseModelEntityDTO
  {
    public Guid? VehicleId { get; set; }
    public Guid? MediaId { get; set; }
    public uint? MediaTypeId { get; set; }
    public uint? MediaCategoryId { get; set; }
    public uint MediaSubCategoryId { get; set; }
    public sbyte Sequence { get; set; }

    public MediaDTO Media { get; set; }

    public AttribvalDTO MediaCategory { get; set; }

    public AttribvalDTO MediaSubCategory { get; set; }

    public MediaTypeDTO MediaType { get; set; }
    public VehicleDTO Vehicle { get; set; }

    public string MediaURL { get; set; }

    public string ExternalId { get; set; }  // used for externally stored (i.e. not in S3) media like videos etc. 

    public bool IsSpinImage { get; set; }
    public bool IsInternal { get; set; }
    public bool IsEnhanced { get; set; }
  }
}