﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OutputCaching;
using System;
using System.Threading;
using System.Threading.Tasks;
using Trading.API.Data.DTO;
using Trading.API.Data.Models;
using Trading.Services.Classes;
using Trading.Services.Interfaces;

// use this for 
namespace Trading.API.Controllers
{
  [Route("api/appraisal")]
  [ApiController]
  [Authorize]
  public class AppraisalController : ControllerBase
  {
    private readonly IAppraisalService _appraisalService;
    
    public AppraisalController(IAppraisalService appraisalService)
    {
      _appraisalService = appraisalService;
    }


    [HttpPost]
    [Route("")]
    public async Task<IActionResult> CreateAppraisal([FromBody] AppraisalDTO appraisalDTO, CancellationToken cancellationToken)
    {
      try
      {
        var entity = await _appraisalService.CreateAppraisal(appraisalDTO, cancellationToken);
        return Ok(entity);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpDelete]
    [Route("{appraisalId}")]
    public async Task<IActionResult> DeleteAppraisal(Guid appraisalId, CancellationToken cancellationToken)
    {
      try
      {
        await _appraisalService.DeleteAppraisal(appraisalId, cancellationToken);
        return Ok();
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("bodyPartGroups")]
    public async Task<IActionResult> GetBodyPartGroups([FromQuery] bool isInternal, CancellationToken cancellationToken)
    {
      try
      {
        var dtos = await _appraisalService.GetBodyPartGroups(isInternal, cancellationToken);
        return Ok(dtos);
      } 
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("damages")]
    public async Task<IActionResult> GetDamages(CancellationToken cancellationToken)
    {
      try
      {
        var dtos = await _appraisalService.GetDamages(cancellationToken);
        return Ok(dtos);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }


    [HttpPatch]
    [Route("{appraisalId}")]
    public async Task<IActionResult> patchAppraisal(Guid appraisalId, [FromBody] JsonPatchDocument<Appraisal> patch, CancellationToken cancellationToken)
    {
      try
      {
        var appraisal = await _appraisalService.PatchAppraisal(appraisalId, patch, cancellationToken);
        return Ok(appraisal);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/vehicle/{vehicleId}/latestAppraisal")]
    public async Task<IActionResult> GetLatestAppraisal(Guid vehicleId, CancellationToken cancellationToken)
    {
      try
      {
        var appraisal = await _appraisalService.GetLatestAppraisal(vehicleId, cancellationToken);
        return Ok(appraisal);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }

    [HttpGet]
    [Route("/api/listing/{advertId}/latest-appraisal")]
    [OutputCache(Duration = 600, PolicyName = "ConditionalCacheWithAuthPolicy")] // incicate policy name
    public async Task<IActionResult> GetLatestAppraisalByAdvert(Guid advertId, CancellationToken cancellationToken)
    {
      try
      {
        var res = await _appraisalService.GetLatestAppraisalByAdvert(advertId, cancellationToken);
        return Ok(res);
      }
      catch (Exception ex)
      {
        return BadRequest(ex);
      }
    }
  }
}
