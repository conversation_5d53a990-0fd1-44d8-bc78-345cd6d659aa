﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Trading.API.Data.DTO.AutoTrader;


namespace Trading.API.Data.DTO.InspectCollect.VehicleData;

public class ICGetValuationDataInternalResponse
{
  public ICVehicleDTO ICVehicle { get; set; }
  public AutoTraderValuationDTO AutoTraderValuation { get; set; }
}

public class ICVehicleDTO : BaseModelEntityDTO
{
  public Guid? CreatedByResponseId { get; set; }
  public string VRM { get; set; }
  public string VIN { get; set; }

  public string MakeName { get; set; }

  public string ModelName { get; set; }

  public string DerivName { get; set; }

  public string EngineCC { get; set; }

  public string TransmissionTypeName { get; set; }

  public string BodyTypeName { get; set; }

  public string FuelTypeName { get; set; }

  public string PlateName { get; set; }

  public string DateRegistered { get; set; }

  public string Doors { get; set; }

  public string VehicleTypeName { get; set; }

  public string Colour { get; set; }

  public string CO2 { get; set; }

  public string Imported { get; set; }

  public string Weight { get; set; }

  public string EuroStatus { get; set; }

  public string PreviousKeepers { get; set; }

  public string BHP { get; set; }


  public string CapID { get; set; }

  public string CapCode { get; set; }

  public bool? ColourChanged { get; set; }

  public string Co2Rating { get; set; }

  public int? YearOfManufacture { get; set; }

  public string ServiceProvider { get; set; }

  public bool LogBook { get; set; }

  public DateTime? LastChangeOfKeeperDate { get; set; }

  public DateTime MOTExpiryDate { get; set; }

  public ICCapDataDTO CapData { get; set; }
  public ICAutoTraderDataDTO AutoTraderData { get; set; }
}
